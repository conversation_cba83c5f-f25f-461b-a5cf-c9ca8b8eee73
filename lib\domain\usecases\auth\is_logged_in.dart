import 'package:dartz/dartz.dart';
import '../../../core/error/failures.dart';
import '../../repositories/auth_repository.dart';
import '../base/usecase.dart';

/// Use case for checking if user is logged in
class IsLoggedIn implements UseCaseNoParams<bool> {
  final AuthRepository repository;

  IsLoggedIn(this.repository);

  @override
  Future<Either<Failure, bool>> call() async {
    return await repository.isLoggedIn();
  }
}
