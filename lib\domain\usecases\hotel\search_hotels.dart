import 'package:dartz/dartz.dart';
import 'package:equatable/equatable.dart';
import '../../../core/error/failures.dart';
import '../../entities/hotel_entity.dart';
import '../../entities/search_entity.dart';
import '../../repositories/hotel_repository.dart';
import '../base/usecase.dart';

/// Use case for searching hotels by query
class SearchHotels implements UseCase<List<HotelEntity>, SearchHotelsParams> {
  final HotelRepository repository;

  SearchHotels(this.repository);

  @override
  Future<Either<Failure, List<HotelEntity>>> call(SearchHotelsParams params) async {
    final searchCriteria = HotelSearchEntity(
      destination: params.query,
      checkInDate: params.checkInDate,
      checkOutDate: params.checkOutDate,
      numberOfGuests: params.guests,
      numberOfRooms: params.rooms,
      filters: HotelSearchFiltersEntity(
        minPrice: params.minPrice,
        maxPrice: params.maxPrice,
        minRating: params.rating,
        amenities: params.amenities,
      ),
    );

    return await repository.searchHotels(searchCriteria);
  }
}

/// Parameters for SearchHotels use case
class SearchHotelsParams extends Equatable {
  final String query;
  final int? cityId;
  final DateTime? checkInDate;
  final DateTime? checkOutDate;
  final int? guests;
  final int? rooms;
  final double? minPrice;
  final double? maxPrice;
  final double? rating;
  final List<String>? amenities;

  const SearchHotelsParams({
    required this.query,
    this.cityId,
    this.checkInDate,
    this.checkOutDate,
    this.guests,
    this.rooms,
    this.minPrice,
    this.maxPrice,
    this.rating,
    this.amenities,
  });

  @override
  List<Object?> get props => [
        query,
        cityId,
        checkInDate,
        checkOutDate,
        guests,
        rooms,
        minPrice,
        maxPrice,
        rating,
        amenities,
      ];
}
