import 'package:dartz/dartz.dart';
import '../../../core/error/failures.dart';
import '../../entities/user_entity.dart';
import '../../repositories/auth_repository.dart';
import '../base/usecase.dart';

/// Use case for getting current authentication state
class GetCurrentAuth implements UseCaseNoParams<AuthEntity?> {
  final AuthRepository repository;

  GetCurrentAuth(this.repository);

  @override
  Future<Either<Failure, AuthEntity?>> call() async {
    return await repository.getCurrentAuth();
  }
}
