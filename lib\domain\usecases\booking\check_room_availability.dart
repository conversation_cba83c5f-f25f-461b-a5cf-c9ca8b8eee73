import 'package:dartz/dartz.dart';
import 'package:equatable/equatable.dart';
import '../../../core/error/failures.dart';
import '../../repositories/booking_repository.dart';
import '../base/usecase.dart';

/// Use case for checking room availability
class CheckRoomAvailability implements UseCase<bool, CheckRoomAvailabilityParams> {
  final BookingRepository repository;

  CheckRoomAvailability(this.repository);

  @override
  Future<Either<Failure, bool>> call(CheckRoomAvailabilityParams params) async {
    return await repository.checkAvailability(
      hotelId: params.hotelId,
      roomId: params.roomId,
      checkInDate: params.checkInDate,
      checkOutDate: params.checkOutDate,
      numberOfRooms: params.numberOfRooms,
    );
  }
}

/// Parameters for CheckRoomAvailability use case
class CheckRoomAvailabilityParams extends Equatable {
  final String hotelId;
  final String roomId;
  final DateTime checkInDate;
  final DateTime checkOutDate;
  final int numberOfRooms;

  const CheckRoomAvailabilityParams({
    required this.hotelId,
    required this.roomId,
    required this.checkInDate,
    required this.checkOutDate,
    required this.numberOfRooms,
  });

  @override
  List<Object> get props => [hotelId, roomId, checkInDate, checkOutDate, numberOfRooms];
}
