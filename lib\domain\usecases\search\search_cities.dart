import 'package:dartz/dartz.dart';
import 'package:equatable/equatable.dart';
import '../../../core/error/failures.dart';
import '../../entities/search_entity.dart';
import '../../repositories/search_repository.dart';
import '../base/usecase.dart';

/// Use case for searching cities
class SearchCities implements UseCase<List<CityEntity>, SearchCitiesParams> {
  final SearchRepository repository;

  SearchCities(this.repository);

  @override
  Future<Either<Failure, List<CityEntity>>> call(SearchCitiesParams params) async {
    return await repository.searchCities(params.query);
  }
}

/// Parameters for SearchCities use case
class SearchCitiesParams extends Equatable {
  final String query;

  const SearchCitiesParams({required this.query});

  @override
  List<Object> get props => [query];
}
