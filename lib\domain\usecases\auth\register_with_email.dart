import 'package:dartz/dartz.dart';
import 'package:equatable/equatable.dart';
import '../../../core/error/failures.dart';
import '../../entities/user_entity.dart';
import '../../repositories/auth_repository.dart';
import '../base/usecase.dart';

/// Use case for email registration
class RegisterWithEmail implements UseCase<AuthEntity, RegisterWithEmailParams> {
  final AuthRepository repository;

  RegisterWithEmail(this.repository);

  @override
  Future<Either<Failure, AuthEntity>> call(RegisterWithEmailParams params) async {
    return await repository.registerWithEmail(
      firstName: params.firstName,
      lastName: params.lastName,
      email: params.email,
      password: params.password,
    );
  }
}

/// Parameters for RegisterWithEmail use case
class RegisterWithEmailParams extends Equatable {
  final String firstName;
  final String lastName;
  final String email;
  final String password;

  const RegisterWithEmailParams({
    required this.firstName,
    required this.lastName,
    required this.email,
    required this.password,
  });

  @override
  List<Object> get props => [firstName, lastName, email, password];
}
