import 'package:dartz/dartz.dart';
import 'package:equatable/equatable.dart';
import '../../../core/error/failures.dart';
import '../../entities/hotel_entity.dart';
import '../../repositories/hotel_repository.dart';
import '../base/usecase.dart';

/// Use case for getting wishlist hotels
class GetWishlistHotels implements UseCaseNoParams<List<HotelEntity>> {
  final HotelRepository repository;

  GetWishlistHotels(this.repository);

  @override
  Future<Either<Failure, List<HotelEntity>>> call() async {
    return await repository.getWishlistHotels();
  }
}

/// Use case for adding hotel to wishlist
class AddToWishlist implements UseCase<void, AddToWishlistParams> {
  final HotelRepository repository;

  AddToWishlist(this.repository);

  @override
  Future<Either<Failure, void>> call(AddToWishlistParams params) async {
    return await repository.addToWishlist(params.hotelId);
  }
}

/// Use case for removing hotel from wishlist
class RemoveFromWishlist implements UseCase<void, RemoveFromWishlistParams> {
  final HotelRepository repository;

  RemoveFromWishlist(this.repository);

  @override
  Future<Either<Failure, void>> call(RemoveFromWishlistParams params) async {
    return await repository.removeFromWishlist(params.hotelId);
  }
}

/// Parameters for AddToWishlist use case
class AddToWishlistParams extends Equatable {
  final int hotelId;

  const AddToWishlistParams({required this.hotelId});

  @override
  List<Object> get props => [hotelId];
}

/// Parameters for RemoveFromWishlist use case
class RemoveFromWishlistParams extends Equatable {
  final int hotelId;

  const RemoveFromWishlistParams({required this.hotelId});

  @override
  List<Object> get props => [hotelId];
}

/// Parameters for IsHotelInWishlist use case
class IsHotelInWishlistParams extends Equatable {
  final int hotelId;

  const IsHotelInWishlistParams({required this.hotelId});

  @override
  List<Object> get props => [hotelId];
}
