import 'package:dartz/dartz.dart';
import '../../../core/error/failures.dart';
import '../../entities/user_entity.dart';
import '../../repositories/user_repository.dart';
import '../base/usecase.dart';

/// Use case for getting user preferences
class GetUserPreferences implements UseCaseNoParams<UserPreferencesEntity> {
  final UserRepository repository;

  GetUserPreferences(this.repository);

  @override
  Future<Either<Failure, UserPreferencesEntity>> call() async {
    return await repository.getUserPreferences();
  }
}
