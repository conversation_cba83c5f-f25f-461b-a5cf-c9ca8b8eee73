import 'package:dartz/dartz.dart';
import '../../../core/error/failures.dart';
import '../../entities/search_entity.dart';
import '../../repositories/search_repository.dart';
import '../base/usecase.dart';

/// Use case for getting all cities
class GetCities implements UseCaseNoParams<List<CityEntity>> {
  final SearchRepository repository;

  GetCities(this.repository);

  @override
  Future<Either<Failure, List<CityEntity>>> call() async {
    return await repository.getCities();
  }
}
