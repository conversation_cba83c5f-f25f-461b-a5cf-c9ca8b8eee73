import 'package:dartz/dartz.dart';
import '../../../core/error/failures.dart';
import '../../entities/user_entity.dart';
import '../../repositories/user_repository.dart';
import '../base/usecase.dart';

/// Use case for getting current user
class GetCurrentUser implements UseCaseNoParams<UserEntity> {
  final UserRepository repository;

  GetCurrentUser(this.repository);

  @override
  Future<Either<Failure, UserEntity>> call() async {
    return await repository.getUserProfile();
  }
}
