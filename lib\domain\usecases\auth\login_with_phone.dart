import 'package:dartz/dartz.dart';
import 'package:equatable/equatable.dart';
import '../../../core/error/failures.dart';
import '../../entities/user_entity.dart';
import '../../repositories/auth_repository.dart';
import '../base/usecase.dart';

/// Use case for phone login
class LoginWithPhone implements UseCase<AuthEntity, LoginWithPhoneParams> {
  final AuthRepository repository;

  LoginWithPhone(this.repository);

  @override
  Future<Either<Failure, AuthEntity>> call(LoginWithPhoneParams params) async {
    return await repository.loginWithPhone(
      phone: params.phone,
      otp: params.otp,
    );
  }
}

/// Parameters for LoginWithPhone use case
class LoginWithPhoneParams extends Equatable {
  final String phone;
  final String otp;

  const LoginWithPhoneParams({
    required this.phone,
    required this.otp,
  });

  @override
  List<Object> get props => [phone, otp];
}
