import 'package:dartz/dartz.dart';
import 'package:equatable/equatable.dart';
import '../../../core/error/failures.dart';
import '../../repositories/booking_repository.dart';
import '../base/usecase.dart';

/// Use case for canceling a booking
class CancelBooking implements UseCase<void, CancelBookingParams> {
  final BookingRepository repository;

  CancelBooking(this.repository);

  @override
  Future<Either<Failure, void>> call(CancelBookingParams params) async {
    return await repository.cancelBooking(params.bookingId);
  }
}

/// Parameters for CancelBooking use case
class CancelBookingParams extends Equatable {
  final String bookingId;

  const CancelBookingParams({required this.bookingId});

  @override
  List<Object> get props => [bookingId];
}
